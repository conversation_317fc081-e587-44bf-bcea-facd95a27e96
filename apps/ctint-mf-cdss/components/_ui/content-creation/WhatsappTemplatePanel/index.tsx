import { Toaster } from '@cdss-modules/design-system/components/_ui/Toast/toaster';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const WhatsappTemplatePanelBody = (props: { parentId: string }) => {
  return <div>WhatsappTemplatePanel {props.parentId}</div>;
};
// Create a client
const queryClient = new QueryClient();

export const WhatsappTemplatePanel = (props: { parentId: string }) => (
  <QueryClientProvider client={queryClient}>
    <WhatsappTemplatePanelBody parentId={props.parentId} />
    <Toaster />
  </QueryClientProvider>
);

export default WhatsappTemplatePanel;
