import _ from 'lodash';
// Global translations
import globalEn from '@cdss-modules/design-system/i18n/locales/en/translation.json';
import globalHK from '@cdss-modules/design-system/i18n/locales/zh-HK/translation.json';
import globalCN from '@cdss-modules/design-system/i18n/locales/zh-HK/translation.json';
import globalJa from '@cdss-modules/design-system/i18n/locales/ja/translation.json';
import { loadRemote } from '@module-federation/runtime';

// Content creation translations (now local)
import contentCreationEn from './content-creation/en/translation.json';
import contentCreationHK from './content-creation/zh-HK/translation.json';
import contentCreationCN from './content-creation/zh-CN/translation.json';
import contentCreationJa from './content-creation/ja/translation.json';

const globalLocales = {
  en: globalEn,
  'zh-HK': globalHK,
  'zh-CN': globalCN,
  ja: globalJa,
};

// Remote translations (Add remote locales for microfrontend here)
const templateLocales = Promise.all([
  loadRemote('template/locales'),
  loadRemote('interaction/locales'),
  loadRemote('msg/locales'),
  loadRemote('manualQueue/locales'),
  loadRemote('superDashboard/locales'),
  loadRemote('call/locales'),
  loadRemote('tdc/locales'),
  loadRemote('report/locales'),
]);

// Local translations
import en from './en/translation.json';
import hk from './zh-HK/translation.json';
import cn from './zh-HK/translation.json';
import ja from './ja/translation.json';

export const mfLocales = {
  en: _.merge({}, en, contentCreationEn),
  'zh-HK': _.merge({}, hk, contentCreationHK),
  'zh-CN': _.merge({}, cn, contentCreationCN),
  ja: _.merge({}, ja, contentCreationJa),
};

export const appLocales = _.merge({}, globalLocales, mfLocales);

export const prepareLocales = async (callback: (localeData: any) => void) => {
  templateLocales.then((allRes: any[]) => {
    const results = allRes.map((res) => res?.mfLocales ?? {});
    const mergedLocales = _.merge({}, appLocales, ...results);
    callback(mergedLocales);
  });
};

export const allLocales = {
  mfLocales,
  appLocales,
  prepareLocales,
};

export default allLocales;
