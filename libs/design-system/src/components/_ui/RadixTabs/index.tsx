import * as Tabs from '@radix-ui/react-tabs';

interface RadixTabsProps {
  defaultOpenTab?: string;
  tabs: {
    tabName: string;
    label: string;
    content: React.ReactNode;
  }[];
}

const RadixTabs: React.FC<RadixTabsProps> = ({ defaultOpenTab, tabs }) => {
  return (
    <Tabs.Root
      defaultValue={defaultOpenTab}
      className="h-full overflow-hidden"
    >
      <Tabs.List className="flex shrink-0 border-b">
        {tabs.map((tab, index) => (
          <Tabs.Trigger
            key={index}
            value={tab.tabName}
            className={`font-bold text-grey-600 px-4 py-2 data-[state=active]:text-other-orange data-[state=active]:shadow-[inset_0_-1px_0_0,0_1px_0_0] data-[state=active]:shadow-current`}
          >
            {tab.label}
          </Tabs.Trigger>
        ))}
      </Tabs.List>
      {tabs.map((tab, index) => (
        <Tabs.Content
          key={index}
          value={tab.tabName}
          className="flex flex-col w-full h-full overflow-auto"
        >
          {tab.content}
        </Tabs.Content>
      ))}
    </Tabs.Root>
  );
};

export default RadixTabs;
